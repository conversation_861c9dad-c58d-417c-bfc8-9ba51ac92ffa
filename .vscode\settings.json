{
	// 保存时使用eslint格式进行修复
	"editor.codeActionsOnSave": {
		"source.fixAll": "explicit",
		"source.fixAll.eslint": "explicit",
		"source.fixAll.stylelint": "explicit"
	},
	// 保存的时候自动格式化
	"editor.formatOnSave": true,
	// Elint
	"eslint.validate": ["javascript", "javascriptreact", "html", "vue"],
	"[vue]": {
		"editor.formatOnSave": true,
		"editor.defaultFormatter": "dbaeumer.vscode-eslint"
	},
	"[javascript]": {
		"editor.formatOnSave": true,
		"editor.defaultFormatter": "dbaeumer.vscode-eslint"
	},
	"[json]": {
		"editor.formatOnSave": true,
		"editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	// Stylelint
	// 关闭默认的 CSS 格式和各种预处理器,由 Stylelint 统一处理
	"scss.validate": false,
	"css.validate": false,
	"less.validate": false,
	"stylelint.validate": [
		"css",
		"less",
		"postcss",
		"scss",
		"vue",
		"sass"
	],
    "i18n-ally.localesPaths": ["src/locales"],
     "i18n-ally.sortKeys": true,
     "i18n-ally.sourceLanguage": "zh-CN", // 翻译源语言 是你翻译文件的名字
     "i18n-ally.displayLanguage": "en-US", //显示语言， 这里也可以设置显示英文为en
	"i18n-ally.enabledParsers": [
		"js",
		"json"
	],
	"i18n-ally.extract.autoDetect": false,
}
